.survey-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.survey-modal {
  background: #fff;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: surveyModalSlideIn 0.3s ease-out;
}

@keyframes surveyModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.survey-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.survey-close:hover {
  background: #f0f0f0;
  color: #333;
}

.survey-content {
  color: #333;
}

.survey-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #8b5cf6;
}

.survey-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.survey-progress {
  font-size: 0.9rem;
  color: #888;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.survey-question {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 2rem 0;
  line-height: 1.4;
}

.survey-answers-container {
  width: 100%;
}

.survey-answers-button {
  width: 100%;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.survey-answers-button:hover {
  background: #7c3aed;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.survey-answers-button.expanded {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.survey-arrow {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.survey-arrow.rotated {
  transform: rotate(180deg);
}

.survey-answers-list {
  background: #f8f9fa;
  border: 2px solid #8b5cf6;
  border-top: none;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  padding: 0.5rem;
  animation: surveyAnswersSlideDown 0.3s ease-out;
}

@keyframes surveyAnswersSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.survey-answer-option {
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  font-size: 0.95rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.survey-answer-option:hover {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
  transform: translateX(4px);
}

.survey-answer-option:active {
  transform: translateX(4px) scale(0.98);
}

/* Responsive design */
@media (max-width: 768px) {
  .survey-modal {
    padding: 1.5rem;
    margin: 1rem;
    width: calc(100% - 2rem);
  }
  
  .survey-title {
    font-size: 1.3rem;
  }
  
  .survey-question {
    font-size: 1.1rem;
  }
  
  .survey-answers-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }
}
