import React, { useState } from 'react';
import NetflixCarousel from './NetflixCarousel';
import SurveyCarousel from './SurveyCarousel';

const CarouselDemo = () => {
  const [surveyAnswers, setSurveyAnswers] = useState(null);
  // Sample data for the carousel
  const trendingNow = [
    {
      title: "Stranger Things",
      description: "When a young boy vanishes, a small town uncovers a mystery involving secret experiments.",
      image: "https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=400&h=225&fit=crop"
    },
    {
      title: "The Crown",
      description: "Follows the political rivalries and romance of Queen <PERSON>'s reign.",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=225&fit=crop"
    },
    {
      title: "Breaking Bad",
      description: "A high school chemistry teacher turned methamphetamine manufacturer.",
      image: "https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=400&h=225&fit=crop"
    },
    {
      title: "The Witcher",
      description: "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, a solitary monster hunter, struggles to find his place.",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=225&fit=crop"
    },
    {
      title: "Ozark",
      description: "A financial advisor drags his family to the Missouri Ozarks.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop"
    },
    {
      title: "House of Cards",
      description: "A ruthless politician will stop at nothing to conquer Washington.",
      image: "https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=225&fit=crop"
    },
    {
      title: "Narcos",
      description: "The true story of Colombia's infamously violent and powerful drug cartels.",
      image: "https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=400&h=225&fit=crop"
    },
    {
      title: "Black Mirror",
      description: "An anthology series exploring a twisted, high-tech multiverse.",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=225&fit=crop"
    }
  ];

  const popularMovies = [
    {
      title: "The Irishman",
      description: "An aging hitman recalls his time with the mob and the intersections.",
      image: "https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=400&h=225&fit=crop"
    },
    {
      title: "Marriage Story",
      description: "Noah Baumbach's incisive and compassionate look at a marriage breaking up.",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=225&fit=crop"
    },
    {
      title: "Roma",
      description: "A year in the life of a middle-class family's maid in Mexico City.",
      image: "https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=400&h=225&fit=crop"
    },
    {
      title: "Bird Box",
      description: "Five years after an ominous unseen presence drives most of society to suicide.",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=225&fit=crop"
    },
    {
      title: "Extraction",
      description: "A black-market mercenary has nothing to lose when his skills are solicited.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop"
    },
    {
      title: "The Platform",
      description: "A vertical prison with one cell per level and two people per cell.",
      image: "https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=225&fit=crop"
    }
  ];

  const documentaries = [
    {
      title: "Our Planet",
      description: "Documentary series focusing on the breadth of the diversity of habitats.",
      image: "https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=400&h=225&fit=crop"
    },
    {
      title: "Making a Murderer",
      description: "Filmed over 10 years, this real-life thriller follows a DNA exoneree.",
      image: "https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=400&h=225&fit=crop"
    },
    {
      title: "Tiger King",
      description: "A rivalry between big cat eccentrics takes a dark turn.",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=225&fit=crop"
    },
    {
      title: "The Social Dilemma",
      description: "Explores the dangerous human impact of social networking.",
      image: "https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=400&h=225&fit=crop"
    }
  ];

  // Survey data for personalized recommendations
  const surveyData = {
    title: "Tailor your discovery",
    subtitle: "Answer these questions to filter the noise.",
    questions: [
      {
        question: "What's your current plan for Uranium Energy Corp?",
        answers: [
          "Buy and hold long-term",
          "Short-term trading opportunity",
          "Waiting for better entry point",
          "Not interested in uranium stocks",
          "Need more research first"
        ]
      },
      {
        question: "What type of content interests you most?",
        answers: [
          "Action & Adventure",
          "Drama & Romance",
          "Comedy & Light-hearted",
          "Documentaries & Educational",
          "Sci-Fi & Fantasy"
        ]
      },
      {
        question: "How do you prefer to discover new content?",
        answers: [
          "Based on my viewing history",
          "Popular trending content",
          "Critics' recommendations",
          "Friends' suggestions",
          "Random exploration"
        ]
      }
    ]
  };

  const handleSurveyComplete = (answers) => {
    setSurveyAnswers(answers);
    console.log('Survey completed with answers:', answers);
    // Here you could filter/personalize content based on answers
  };

  return (
    <div style={{ background: '#141414', minHeight: '100vh', padding: '2rem 0' }}>
      <SurveyCarousel
        title="Trending Now"
        items={trendingNow}
        surveyData={surveyData}
        onSurveyComplete={handleSurveyComplete}
      />
      <NetflixCarousel title="Popular Movies" items={popularMovies} />
      <NetflixCarousel title="Documentaries" items={documentaries} />
    </div>
  );
};

export default CarouselDemo;
