.survey-card {
  flex: 0 0 280px;
  height: 157px;
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.survey-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.survey-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.survey-card-title {
  color: #8b5cf6;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.survey-card-subtitle {
  color: #666;
  font-size: 0.8rem;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.survey-card-progress {
  color: #888;
  font-size: 0.7rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.survey-card-question {
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  flex-grow: 1;
}

.survey-answers-container {
  position: relative;
}

.survey-answers-button {
  width: 100%;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.survey-answers-button:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.survey-answers-button.expanded {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.survey-arrow {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

.survey-arrow.rotated {
  transform: rotate(180deg);
}

.survey-answers-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #f8f9fa;
  border: 2px solid #8b5cf6;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 0.25rem;
  z-index: 100;
  max-height: 200px;
  overflow-y: auto;
  animation: surveyAnswersSlideDown 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@keyframes surveyAnswersSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.survey-answer-option {
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  margin: 0.125rem 0;
  font-size: 0.75rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  line-height: 1.2;
}

.survey-answer-option:hover {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
  transform: translateX(2px);
}

.survey-answer-option:active {
  transform: translateX(2px) scale(0.98);
}

/* Completed state */
.survey-card.completed {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  text-align: center;
  justify-content: center;
  align-items: center;
}

.survey-completed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.survey-completed-icon {
  margin-bottom: 0.5rem;
}

.survey-completed-icon svg {
  width: 24px;
  height: 24px;
  color: #fbbf24;
}

.survey-completed-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.survey-completed-text {
  font-size: 0.8rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .survey-card {
    flex: 0 0 200px;
    height: 112px;
    padding: 1rem;
  }
  
  .survey-card-title {
    font-size: 0.9rem;
  }
  
  .survey-card-subtitle {
    font-size: 0.7rem;
  }
  
  .survey-card-question {
    font-size: 0.8rem;
  }
  
  .survey-answers-button {
    padding: 0.4rem 0.6rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .survey-card {
    flex: 0 0 150px;
    height: 84px;
    padding: 0.75rem;
  }
  
  .survey-card-title {
    font-size: 0.8rem;
  }
  
  .survey-card-subtitle {
    font-size: 0.65rem;
    margin-bottom: 0.5rem;
  }
  
  .survey-card-question {
    font-size: 0.7rem;
  }
  
  .survey-answers-button {
    padding: 0.3rem 0.5rem;
    font-size: 0.65rem;
  }
}
