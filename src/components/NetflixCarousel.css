.netflix-carousel {
  margin: 2rem 0;
  position: relative;
}

.carousel-title {
  color: #fff;
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-left: 4%;
}

.carousel-container {
  position: relative;
  padding: 0 4%;
}

.carousel-content {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 1rem 0;
}

.carousel-content::-webkit-scrollbar {
  display: none;
}

.carousel-item {
  flex: 0 0 280px;
  height: 157px;
  position: relative;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.carousel-item:hover {
  transform: scale(1.05);
  z-index: 10;
}

.item-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background: #333;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-item:hover .item-image {
  transform: scale(1.1);
}

.item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1rem;
}

.carousel-item:hover .item-overlay {
  opacity: 1;
}

.item-info {
  color: #fff;
}

.item-title {
  font-size: 1rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.item-description {
  font-size: 0.8rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
}

.play-button,
.info-button {
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.7rem;
  font-weight: 600;
}

.play-button {
  background: #fff;
  color: #000;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.8);
}

.info-button {
  background: rgba(42, 42, 42, 0.6);
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.info-button:hover {
  border-color: #fff;
  background: rgba(42, 42, 42, 0.8);
}

.play-button svg,
.info-button svg {
  width: 16px;
  height: 16px;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;
  opacity: 0;
}

.carousel-container:hover .carousel-arrow {
  opacity: 1;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
  left: -25px;
}

.carousel-arrow-right {
  right: -25px;
}

.carousel-arrow svg {
  width: 24px;
  height: 24px;
}

/* Responsive design */
@media (max-width: 768px) {
  .carousel-item {
    flex: 0 0 200px;
    height: 112px;
  }
  
  .carousel-title {
    font-size: 1.2rem;
    padding-left: 2%;
  }
  
  .carousel-container {
    padding: 0 2%;
  }
  
  .carousel-arrow {
    width: 40px;
    height: 40px;
  }
  
  .carousel-arrow svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .carousel-item {
    flex: 0 0 150px;
    height: 84px;
  }
  
  .item-title {
    font-size: 0.9rem;
  }
  
  .item-description {
    font-size: 0.7rem;
  }
  
  .play-button,
  .info-button {
    width: 28px;
    height: 28px;
  }
}
