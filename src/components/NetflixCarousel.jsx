import React, { useState, useRef, useEffect } from 'react';
import './NetflixCarousel.css';

const NetflixCarousel = ({ title, items }) => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const carouselRef = useRef(null);

  const itemWidth = 300; // Width of each item including margin
  const visibleItems = Math.floor(window.innerWidth / itemWidth);

  useEffect(() => {
    const handleResize = () => {
      checkArrows();
    };
    
    window.addEventListener('resize', handleResize);
    checkArrows();
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const checkArrows = () => {
    if (carouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scroll = (direction) => {
    if (carouselRef.current) {
      const scrollAmount = itemWidth * 3; // Scroll 3 items at a time
      const newScrollPosition = direction === 'left' 
        ? Math.max(0, scrollPosition - scrollAmount)
        : scrollPosition + scrollAmount;
      
      carouselRef.current.scrollTo({
        left: newScrollPosition,
        behavior: 'smooth'
      });
      
      setScrollPosition(newScrollPosition);
      
      // Check arrows after scroll animation
      setTimeout(checkArrows, 300);
    }
  };

  return (
    <div className="netflix-carousel">
      <h2 className="carousel-title">{title}</h2>
      <div className="carousel-container">
        {showLeftArrow && (
          <button 
            className="carousel-arrow carousel-arrow-left"
            onClick={() => scroll('left')}
            aria-label="Scroll left"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </button>
        )}
        
        <div 
          className="carousel-content"
          ref={carouselRef}
          onScroll={checkArrows}
        >
          {items.map((item, index) => (
            <div key={index} className="carousel-item">
              <div className="item-image-container">
                <img 
                  src={item.image} 
                  alt={item.title}
                  className="item-image"
                />
                <div className="item-overlay">
                  <div className="item-info">
                    <h3 className="item-title">{item.title}</h3>
                    <p className="item-description">{item.description}</p>
                    <div className="item-actions">
                      <button className="play-button">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                        Play
                      </button>
                      <button className="info-button">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {showRightArrow && (
          <button 
            className="carousel-arrow carousel-arrow-right"
            onClick={() => scroll('right')}
            aria-label="Scroll right"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default NetflixCarousel;
