import React, { useState } from 'react';
import './SurveyCard.css';

const SurveyCard = ({ surveyData, onSurveyComplete, isCompleted }) => {
  const [showAnswers, setShowAnswers] = useState(false);

  const toggleAnswers = () => {
    setShowAnswers(!showAnswers);
  };

  const handleAnswerSelect = (answer) => {
    // For now, just complete the survey with the first answer
    // In a real implementation, you'd handle the full survey flow
    onSurveyComplete({ 0: answer });
  };

  if (isCompleted) {
    return (
      <div className="survey-card completed">
        <div className="survey-completed-content">
          <div className="survey-completed-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <h3 className="survey-completed-title">Personalized!</h3>
          <p className="survey-completed-text">Your recommendations are now tailored to your preferences.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-card">
      <div className="survey-card-content">
        <h3 className="survey-card-title">{surveyData.title}</h3>
        <p className="survey-card-subtitle">{surveyData.subtitle}</p>
        
        <div className="survey-card-progress">
          Question 1 of {surveyData.questions.length}
        </div>
        
        <h4 className="survey-card-question">
          {surveyData.questions[0].question}
        </h4>
        
        <div className="survey-answers-container">
          <button 
            className={`survey-answers-button ${showAnswers ? 'expanded' : ''}`}
            onClick={toggleAnswers}
          >
            <span>Answers</span>
            <svg 
              className={`survey-arrow ${showAnswers ? 'rotated' : ''}`}
              viewBox="0 0 24 24" 
              fill="currentColor"
            >
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>
          
          {showAnswers && (
            <div className="survey-answers-list">
              {surveyData.questions[0].answers.map((answer, index) => (
                <button
                  key={index}
                  className="survey-answer-option"
                  onClick={() => handleAnswerSelect(answer)}
                >
                  {answer}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SurveyCard;
