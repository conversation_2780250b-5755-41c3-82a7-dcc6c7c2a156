import React, { useState } from 'react';
import './SurveyModal.css';

const SurveyModal = ({ isOpen, onClose, onComplete, surveyData }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [showAnswers, setShowAnswers] = useState(false);

  if (!isOpen || !surveyData) return null;

  const handleAnswerSelect = (answer) => {
    const newAnswers = {
      ...answers,
      [currentQuestion]: answer
    };
    setAnswers(newAnswers);

    // Move to next question after a short delay
    setTimeout(() => {
      if (currentQuestion < surveyData.questions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
        setShowAnswers(false);
      } else {
        // Survey complete
        onComplete(newAnswers);
      }
    }, 500);
  };

  const toggleAnswers = () => {
    setShowAnswers(!showAnswers);
  };

  const currentQ = surveyData.questions[currentQuestion];

  return (
    <div className="survey-modal-overlay" onClick={onClose}>
      <div className="survey-modal" onClick={(e) => e.stopPropagation()}>
        <button className="survey-close" onClick={onClose}>×</button>
        
        <div className="survey-content">
          <h2 className="survey-title">{surveyData.title}</h2>
          <p className="survey-subtitle">{surveyData.subtitle}</p>
          
          <div className="survey-progress">
            Question {currentQuestion + 1} of {surveyData.questions.length}
          </div>
          
          <h3 className="survey-question">{currentQ.question}</h3>
          
          <div className="survey-answers-container">
            <button 
              className={`survey-answers-button ${showAnswers ? 'expanded' : ''}`}
              onClick={toggleAnswers}
            >
              <span>Answers</span>
              <svg 
                className={`survey-arrow ${showAnswers ? 'rotated' : ''}`}
                viewBox="0 0 24 24" 
                fill="currentColor"
              >
                <path d="M7 10l5 5 5-5z"/>
              </svg>
            </button>
            
            {showAnswers && (
              <div className="survey-answers-list">
                {currentQ.answers.map((answer, index) => (
                  <button
                    key={index}
                    className="survey-answer-option"
                    onClick={() => handleAnswerSelect(answer)}
                  >
                    {answer}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveyModal;
