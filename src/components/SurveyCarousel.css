.survey-carousel {
  position: relative;
}

.carousel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-left: 4%;
  padding-right: 4%;
}

.carousel-header .carousel-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.personalized-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.personalized-badge svg {
  width: 12px;
  height: 12px;
}

.survey-trigger-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.survey-trigger-button:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.survey-trigger-button svg {
  width: 16px;
  height: 16px;
}

/* Animation for when carousel becomes personalized */
.survey-carousel.personalized {
  animation: personalizedGlow 0.8s ease-out;
}

@keyframes personalizedGlow {
  0% {
    box-shadow: 0 0 0 rgba(139, 92, 246, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 0 rgba(139, 92, 246, 0);
  }
}

/* Enhanced carousel items for personalized content */
.survey-carousel.personalized .carousel-item {
  position: relative;
}

.survey-carousel.personalized .carousel-item::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #8b5cf6, #a855f7, #8b5cf6);
  border-radius: 6px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.survey-carousel.personalized .carousel-item:hover::before {
  opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .carousel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding-left: 2%;
    padding-right: 2%;
  }
  
  .survey-trigger-button {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
  
  .personalized-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .carousel-header .carousel-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .survey-trigger-button {
    align-self: stretch;
    justify-content: center;
  }
}
